<!--
 * @Description:
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-16 10:32:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-30 15:36:19
-->
<template>
  <div class="containerWrapper">
    <div class="top-section">
      <!-- 第一行：按钮 -->
      <div class="button-section" v-if="isCustomBtn">
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-document-add"
          @click="handleAdd"
          >添加</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-edit"
          @click="handleEdit"
          :disabled="!treeId"
          >修改</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-delete"
          @click="handleDelete"
          :disabled="!treeId"
          >删除</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-view"
          @click="handlePreview"
          >合并预览</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-back"
          @click="handleBack"
          >返回</el-button
        >
      </div>
      <div class="button-section" v-else>
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-view"
          @click="handlePreview"
          >合并预览</el-button
        >
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-view"
          @click="handleSmartCheck"
        >智能辅助检查</el-button>
        <el-button
          class="pur-button-list custom-button"
          icon="el-icon-view"
          @click="handleNewFile"
          v-if="isSupplier"
          >{{crateFileText}}</el-button
        >

        <div class="tags" v-if="isShowAnnotate">
          <span style="text-align: center; width: 85px; line-height: 32px"
            >请输入批注: &nbsp;&nbsp;</span
          >
          <el-input
            v-model="annotate"
            placeholder="请输入批注内容"
            style="width: 80%"
            maxlength="200"
            show-word-limit
            :disabled="emptText || isEditBtn==='否' || moduleType === '编制采购文件'"
          ></el-input> &nbsp;&nbsp;
            <el-button
              type="primary"
              v-if="isShowAnnotate"
              :disabled="emptText || moduleType === '编制采购文件'"
              @click="handleSure('annotate')"
              :loading="isSLing"
              >保存批注</el-button
            >
        </div>
      </div>
    </div>
    <div class="main-section">
      <!-- 左侧树形菜单 -->
      <div class="tree-section" :class="treeCollapsed ? 'collapsed' : ''">
        <!-- 缩进按钮 -->
        <div class="retract-block retract-left" style="cursor:pointer;" @click="treeCollapsed = !treeCollapsed">
          <i class="el-icon-arrow-right" v-if="treeCollapsed" style="font-size:13px;cursor:pointer;"></i>
          <i class="el-icon-arrow-left" v-if="!treeCollapsed" style="font-size:13px;cursor:pointer;"></i>
        </div>
        <div class="tree-content">
          <div class="tree-inner" v-show="!treeCollapsed">
            <el-input
              placeholder="输入关键字进行过滤"
              v-model="filterText"
            ></el-input>
            <el-tree
              :data="treeData"
              node-key="id"
              :expand-on-click-node="false"
              highlight-current
              @node-click="handleNodeClick"
              :default-expand-all="expandAll"
              :props="{ label: 'name', children: 'children' }"
              :filter-node-method="filterNode"
              ref="tree"
              style="margin-top: 10px"
            >
              <template #default="{ node, data }">
                  <span class="custom-node">
                    <i class="red-dot" v-if="moduleType === '编制采购文件' && data.needRevise === '是'"></i>
                    {{ node.label }}
                  </span>
              </template>
              </el-tree>
          </div>
        </div>
      </div>
      <!-- 中间表格 -->
      <div class="table-section" v-if="showContent">
        <p class="preview-text" v-if="emptText">暂无数据</p>
        <!-- 资格性审查项 / 符合性审查项  -->
        <!--  -->
        <zgTable
          ref="zgTable"
          :tableData="tableData"
          :treeId="treeId"
          v-if="contentCategory === '资格性审查项'"
          classificationType="资格性审查"
          :isCustomBtn="isCustomBtn"
          :ifEdit="isCustomBtn ? false : ifEdit || isDetail || isAudit"
          :isAudit="isAudit"
          :isSupplier="isSupplier"
          table-height="calc(70vh - 100px)"
          :is-bottom-btn="true"
          @init="handleNodeClick(treeObj)"
        ></zgTable>
        <fhTable
          ref="fhTable"
          :tableData="tableData"
          :treeId="treeId"
          v-if="contentCategory === '符合性审查项'"
          classificationType="符合性审查"
          :isCustomBtn="isCustomBtn"
          :ifEdit="isCustomBtn ? false : ifEdit || isDetail|| isAudit"
          :isAudit="isAudit"
          :isSupplier="isSupplier"
          table-height="calc(70vh - 100px)"
          :is-bottom-btn="true"
          @init="handleNodeClick(treeObj)"
        ></fhTable>
        <!-- 打分表 -->
        <dfTable
          v-if="contentCategory === '打分表'"
          :tableData="tableData"
          :isCustomBtn="isCustomBtn"
          :treeId="treeId"
          :module="module"
          :ifEdit="isCustomBtn ? false : ifEdit || isDetail|| isAudit"
          :isAudit="isAudit"
          :isSupplier="isSupplier"
          @init="handleNodeClick(treeObj)"
        ></dfTable>
        <!-- 技术规格偏离 -->
        <jsggTable
          v-if="contentCategory === '技术规格偏离表'"
          :tableData="tableData"
          :isCustomBtn="isCustomBtn"
          :treeId="treeId"
          :ifEdit="isCustomBtn ? false : ifEdit || isDetail|| isAudit"
          :isAudit="isAudit"
          :isSupplier="isSupplier"
          tableDefaultHeight="calc(70vh - 100px)"
          :is-bottom-btn="true"
          @init="handleNodeClick(treeObj)"
        ></jsggTable>
        <!-- 商务需求偏离表 -->
        <swyqTable
          v-if="contentCategory === '商务需求偏离表'"
          :tableData="tableData"
          :isCustomBtn="isCustomBtn"
          :treeId="treeId"
          :ifEdit="isCustomBtn ? false : ifEdit || isDetail|| isAudit"
          :isAudit="isAudit"
          :isSupplier="isSupplier"
          tableDefaultHeight="calc(70vh - 100px)"
          :is-bottom-btn="true"
          @init="handleNodeClick(treeObj)"
        ></swyqTable>
        <!-- 根据条件展示 富文本/表格 -->
        <!--  -->
        <div style="width: 100%;overflow:hidden;position: relative;" v-if="contentType === '富文本'">
          <myTinymce
            :editHeight="!isCustomBtn === false ? '350' : '580'"
            :style="{ height: !isCustomBtn === false ? 'calc(80vh - 100px)' : '580px' }"
            v-model="tinymceContent"
            :isDisabled="isCustomBtn ? false : ifEdit || isDetail || isAudit"
            ref="tinymceRef"
            v-if="showContent"
          />
          <div class="footer" v-if="isAudit">
            <!-- 添加审核意见 -->
            <div class="audit-review-section">
              <div class="audit-title">添加审核意见</div>
              <div class="audit-form">
                <div class="audit-form-left">
                  <el-input
                    type="textarea"
                    v-model="auditComment"
                    placeholder="请输入审核意见"
                    :rows="1"
                    maxlength="500"
                    show-word-limit
                    class="audit-textarea"
                  ></el-input>
                </div>
                <div class="audit-form-right">
                  <el-button
                    type="primary"
                    @click="handleSaveAuditComment"
                    :loading="auditSaving"
                    :disabled="!auditComment.trim()"
                    class="audit-save-btn"
                  >
                    保存
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div class="footer" v-else>
            <div class="keyWord-conetent" v-if="isShowKeywords">
              <span>请输入获取总金额的前缀关键字： &nbsp;</span>
              <el-input
                v-model="totalAmountPrefixMark"
                maxlength="10"
                min="1"
                placeholder="系统将获取前缀关键字'xxx',后缀关键字'元'之间的数值作为总金额"
                :disabled="isSaved"
                style="width: 150px; margin-right: 15px"
              ></el-input>
            </div>
            <div  v-if="cusName === '特殊企业声明函'" style="margin-right: 15px;">
                <el-select
                v-model="discountType"
                placeholder="请选择项目类别"
                class="cusSelect"
                @change="projectTypeChange"
              >
                <el-option
                  v-for="item in fhoptions"
                  :key="item.bizid"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              </div>
            <el-button
              class="custom-button"
              icon="el-icon-upload2"
              v-show="!isSupplier && (contentType === '富文本' && isCustomBtn)"
              :disabled="isCustomBtn ? false :(ifEdit || !treeId) || isAudit"
              @click="handleUpload"
              >导入文件内容</el-button
            >
            <el-button
              type="primary"
              v-if="!isAudit"
              :disabled="isCustomBtn ? false :(ifEdit || isSaved)"
              @click="handleSure"
              :loading="isSLing"
              >保存</el-button
            >
          </div>
          <p v-if="isShowKeywords" style="text-align: center">
            系统将获取前缀关键字"<b style="color: #3e3b3b">{{
              totalAmountPrefixMark
            }}</b
            >" ,后缀关键字
            <b style="color: #3e3b3b">"元"</b>之间的数值作为总金额
          </p>
        </div>
      </div>
      <!-- 右侧 智能辅助检查列表 -->
      <div class="smart-check-container" v-if="showSmartCheck" :class="smartCheckCollapsed ? 'collapsed' : ''">
        <!-- 缩进按钮 -->
        <div class="retract-block retract-right" style="cursor:pointer;" @click="smartCheckCollapsed = !smartCheckCollapsed">
          <i class="el-icon-arrow-left" v-if="smartCheckCollapsed" style="font-size:13px;cursor:pointer;"></i>
          <i class="el-icon-arrow-right" v-if="!smartCheckCollapsed" style="font-size:13px;cursor:pointer;"></i>
        </div>
        <div class="list-section">
          <div class="smart-check-panel" v-show="!smartCheckCollapsed">
            <!-- 主标签页导航 -->
            <el-tabs v-model="activeMainTab" class="main-tabs">
              <el-tab-pane
                v-for="mainTab in mainTabs"
                :key="mainTab.key"
                :name="mainTab.key"
                :label="mainTab.label"
              >
                <!-- 人工审核发现的问题 -->
                <div v-if="mainTab.key === 'manual-issues'" class="tab-content manual-issues-content">
                  <manual-review-issues
                    :issues="manualReviewData.issues"
                    @locate="handleLocateOriginal"
                    @edit-item="handleEditManualItem"
                    @save-item="handleSaveManualItem"
                    @delete-item="handleDeleteManualItem"
                  />
                </div>

                <!-- 智能辅助检查结果 -->
                <div v-if="mainTab.key === 'smart-check'" class="smart-check-content">
                  <!-- 智能检查子标签页 -->
                  <el-tabs v-model="activeSmartCheckTab" class="smart-check-tabs" type="card">
                    <el-tab-pane
                      v-for="tab in smartCheckTabs"
                      :key="tab.key"
                      :name="tab.key"
                    >
                    <span slot="label">
                      {{ tab.label }}
                      <el-badge
                        :value="getTabCount(tab.key)"
                        :max="99"
                        class="tab-badge"
                        v-if="getTabCount(tab.key) > 0"
                      />
                    </span>

                      <!-- 列表内容 -->
                      <div class="tab-content">
                        <div v-if="getTabItems(tab.key).length === 0" class="empty-state">
                          暂无{{ tab.label }}
                        </div>
                        <smart-check-item
                          v-for="item in getTabItems(tab.key)"
                          :key="item.id"
                          :item="item"
                          @locate="handleLocateOriginal"
                          @accept="handleAcceptItem"
                          @reject="handleRejectItem"
                        />
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>

                <!-- 人工审核结果 -->
                <div v-if="mainTab.key === 'manual-results'" class="tab-content manual-results-content">
                  <manual-review-form
                    :initial-data="manualReviewFormData"
                    :show-empty-state="false"
                    @save="handleSaveManualReview"
                    @save-success="handleManualReviewSaveSuccess"
                    @reset="handleResetManualReview"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>
    <moduleChange ref="moduleChange" @init="refresh"></moduleChange>
    <mergeView ref="mergeView"
    ></mergeView>
    <wordImp
      ref="wordImp"
      @init="handleNodeClick(treeObj)"
      :treeId="treeId"
    ></wordImp>
    <viewTemplate
    ref="viewTemplate"
    @changeContent="changeContent"
    :isCustomBtn="isCustomBtn"
    :isAudit="isAudit"
    :isSupplier="isSupplier"
    ></viewTemplate>
    <createFileRef ref="createFileRef"> </createFileRef>
  </div>
</template>

<script>
import moduleChange from "./moduleChange.vue";
import MyTinymce from "@/components/tinymce/myTinymce";
import mergeView from "./mergeView.vue";
import wordImp from "./wordImp.vue";
import zgTable from "./zgTable.vue";
import fhTable from "./fhTable.vue";
import jsggTable from "./jsggTable.vue";
import swyqTable from "./swyqTable.vue";
import dfTable from "./dfTable.vue";
import viewTemplate from "./viewTemplate.vue";
import createFileRef from './createFileRef';
import smartCheckItem from './smartCheckItem.vue';
import manualReviewIssues from './manualReviewIssues.vue';
import manualReviewForm from './manualReviewForm.vue';

export default {
  components: {
    moduleChange,
    MyTinymce,
    mergeView,
    wordImp,
    zgTable,
    fhTable,
    jsggTable,
    swyqTable,
    dfTable,
    viewTemplate,
    createFileRef,
    smartCheckItem,
    manualReviewIssues,
    manualReviewForm
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  props: {
    isCustomBtn: {
      type: Boolean,
      default: true,
    },
    // 是否为自定义模块
    projectInfoBizid: {
      type: String,
      default: "",
    },
    // 项目id
    projectInfoStatus: {
      type: String,
      default: "",
    },
    // 项目状态
    isAudit: {
      type: Boolean,
      default: false,
    },
    isSupplier:{
      type: Boolean,
      default: false
    },
    // 是否供应商
    moduleType: {
       type: String,
       default: ""
    },
    // 模块类型
    projectStatus: {
      type: String,
      default: "",
    },
    // 项目状态
    isDetail: {
      type: Boolean,
      default: false,
    },
    // 是否详情
    crateFileText: {
      type: String,
      default: "生成文件"
    },
    module:{
      type: String,
      default: ""
    },
    // 智能检查标签页配置
    smartCheckTabConfig: {
      type: String,
      default: 'one', // 'one', 'two', 'three'
      validator: function (value) {
        return ['one', 'two', 'three'].indexOf(value) !== -1
      }
    }
  },
  computed: {
    isShowKeywords() {
      return (
        ["投标一览表", "投标分项报价清单"].includes(this.contentCategory) && !this.isSupplier
      );
    },
    isShowAnnotate() {
      return false;
      if( ["投标一览表", "投标分项报价清单"].includes(this.contentCategory) && this.isEditBtn==='是' && !this.isDetail) {
        // 投标一览表 不需要展示批注 其他都要
        return false
      } else if(this.isAudit && this.isEditBtn==='是'&& !this.isDetail) {
         // 审核页  1.批注  如果是点击的项是可编辑的 显示批注输入框  批注：文本域 200
        return true
      } else if(this.moduleType === '编制采购文件'&& this.isEditBtn==='是' && this.projectStatus.includes('不通过') && !this.isDetail) {
        // 采购文件制作： 状态只要为不通过的并且是可编辑的  显示批注  批注：文本域
        return true
      }
    },
    // 动态主标签页配置
    mainTabs() {
      const tabConfigs = {
        'one': [
          { key: 'smart-check', label: '智能辅助检查结果' }
        ],
        'two': [
          { key: 'manual-issues', label: '人工审核发现的问题' },
          { key: 'smart-check', label: '智能辅助检查结果' }
        ],
        'three': [
          { key: 'manual-issues', label: '人工审核发现的问题' },
          { key: 'smart-check', label: '智能辅助检查结果' },
          { key: 'manual-results', label: '人工审核结果' }
        ]
      };
      return tabConfigs[this.smartCheckTabConfig] || tabConfigs['one'];
    }
  },
  data() {
    return {
      title: "配置模版",
      dialogVisible: false,
      tinymceContent: "",
      filterText: "",
      treeData: [],
      // 树形菜单数据
      defaultExpand: [],
      purFileTemplateBizid: "",
      treeId: "",
      treeObj: {},
      contentType: "",
      emptText: true,
      isEdit: false,
      isSaved: false,
      showContent: true, //表格/富文本
      tableData: [],
      loading: false,
      rowIndex: 0,
      randNum: 1 + Math.random(),
      contentCategory: "",
      isSLing: false,
      totalAmountPrefixMark: "",
      ifEdit: false,
      annotate: "",
      isEditBtn: '',
      expandAll: true,
      cusName: "",
      fhoptions: [
        { value: '小微企业', name: '小微企业' },
        { value: '监狱企业', name: '监狱企业' },
        { value: '残疾人福利性单位', name: '残疾人福利性单位' },
        { value: '以上都不是', name: '以上都不是' }
      ],
      discountType: "",
      // 智能辅助检查相关数据
      showSmartCheck: false,
      smartCheckCollapsed: false,
      // 左侧树形菜单收缩状态
      treeCollapsed: false,
      // 主要标签页配置
      activeMainTab: 'smart-check',
      // 智能检查子标签页
      activeSmartCheckTab: 'completeness',
      smartCheckTabs: [
        { key: 'completeness', label: '完整性问题' },
        { key: 'consistency', label: '一致性问题' },
        { key: 'logic', label: '逻辑性问题' },
        { key: 'compliance', label: '合规性问题' }
      ],
      smartCheckData: {
        completeness: [],
        consistency: [],
        logic: [],
        compliance: []
      },
      // 人工审核数据
      manualReviewData: {
        issues: [],
        results: []
      },
      // 人工审核表单数据
      manualReviewFormData: {
        reviewComment: '',
        reviewResult: ''
      },
      // 审核意见相关数据
      auditComment: '',
      auditSaving: false
    };
  },
  methods: {
    init(purFileTemplateBizid) {
      if (purFileTemplateBizid) {
        this.purFileTemplateBizid = purFileTemplateBizid;
      }
      this.contentCategory = "";
      this.contentType = "";
      this.emptText = true
      let fetchApi
      let obj = {}
      if(!this.isCustomBtn && !this.isSupplier) {
        fetchApi = "treePurFileConfigCatalog"
        obj = { projectInfoBizid: this.projectInfoBizid }
      } else if(this.isSupplier) {
        obj = { projectInfoBizid: this.projectInfoBizid}
        fetchApi = "treeBidFileConfigCatalog"
      } else {
        fetchApi = "treePurFileTemplateCatalog"
        obj = { purFileTemplateBizid: this.purFileTemplateBizid }
      }
      this.$callApiParams(`${fetchApi}`, obj, (result) => {
        this.treeData = result.data;
        this.treeId = "";
        // result?.data?.length > 0 &&  this.handleNodeClick(result?.data[0])
        return true;
      });
    },
    // 操作刷新选中树
    refresh(id) {
      this.treeId = id;
      this.init()
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(id)
      })
    },
    // 投标报名生成文件
    handleNewFile() {
      this.$refs.createFileRef.handleOpen(this.projectInfoBizid)
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleBack() {
      this.$emit("handleBack");
    },
    handleAdd() {
      this.$refs.moduleChange.handleOpen(
        "add",
        this.treeData,
        this.purFileTemplateBizid,
        this.treeObj
      );
    },
    handleEdit() {
      // 处理修改逻辑
      if (!this.treeId) {
        this.$message.warning("请选择要修改的节点");
        return;
      }
      this.$refs.moduleChange.handleOpen(
        "edit",
        this.treeData,
        this.purFileTemplateBizid,
        this.treeObj
      );
    },
    handleDelete() {
      // 处理删除逻辑
      if (!this.treeId) {
        this.$message.warning("请选择要删除的节点");
        return;
      }

      this.$confirm("确定删除选中的项目?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$callApi(
          "deletePurFileTemplateCatalog&ids=" + this.treeId,
          {},
          (result) => {
            this.init();
            this.treeId = "";
          }
        );
      });
    },
    handleUpload() {
      // 处理上传文件逻辑
      if (!this.treeId) {
        this.$message.warning("请选择要上传文件的节点");
        return;
      }
      this.$refs.wordImp.openDialog();
    },
    handlePreview() {
      // 处理合并预览逻辑
      let bizid
      if(!this.isCustomBtn) {
        bizid =  this.projectInfoBizid
      } else {
        bizid = this.purFileTemplateBizid
      }

      this.$refs.viewTemplate.handleOpen(bizid, '合并预览');
    },
    handleSmartCheck() {
      // 智能辅助检查
      console.log('Smart check button clicked, moduleType:', this.moduleType);
      console.log('Current smartCheckTabConfig:', this.smartCheckTabConfig);

      this.showSmartCheck = !this.showSmartCheck;
      if (this.showSmartCheck) {
        this.smartCheckCollapsed = false; // 默认展开状态
        this.loadSmartCheckData();
        this.loadManualReviewData();
        // 根据配置设置默认激活的主标签页
        this.activeMainTab = this.mainTabs[0].key;

        console.log('Smart check panel opened, active main tab:', this.activeMainTab);
        console.log('Available main tabs:', this.mainTabs);
      }
    },
    loadSmartCheckData() {
      // 调用 getCheckResultList API 获取智能检查数据
      if (!this.projectInfoBizid) {
        console.warn('projectInfoBizid is required for loading smart check data');
        return;
      }

      const params = {
        page: '制作',
        bizid: this.projectInfoBizid,
        type: '智能检查'
      };

      console.log('Calling getCheckResultList API with params:', params);

      this.$callApiParams('getCheckResultList', params, (result) => {
        console.log('getCheckResultList API response:', result);

        if (result.success && result.data) {
          this.processSmartCheckData(result.data);
        } else {
          console.error('Failed to load smart check data:', result.message || 'Unknown error');
          this.$message.warning('获取智能检查数据失败，请稍后重试');
          // 初始化为空数据
          this.smartCheckData = {
            completeness: [],
            consistency: [],
            logic: [],
            compliance: []
          };
        }
        return true;
      }, (error) => {
        console.error('Error loading smart check data:', error);
        this.$message.error('网络错误，无法获取智能检查数据');
        // 初始化为空数据
        this.smartCheckData = {
          completeness: [],
          consistency: [],
          logic: [],
          compliance: []
        };
      });
    },

    // 处理智能检查数据
    processSmartCheckData(data) {
      // 初始化数据结构
      const processedData = {
        completeness: [],
        consistency: [],
        logic: [],
        compliance: []
      };

      // 检查类型映射
      const checkTypeMapping = {
        '完整性': 'completeness',
        '一致性': 'consistency',
        '逻辑性': 'logic',
        '合规性': 'compliance'
      };

      // 处理每个数据项
      if (Array.isArray(data)) {
        data.forEach(item => {
          const checkType = item.checkType;
          const mappedKey = checkTypeMapping[checkType];

          if (mappedKey && processedData[mappedKey]) {
            // 为每个项目添加唯一ID（如果没有的话）
            const processedItem = {
              ...item,
              id: item.id || `${mappedKey}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              status: item.status || 'pending' // 默认状态
            };

            processedData[mappedKey].push(processedItem);
          } else {
            console.warn(`Unknown checkType: ${checkType}`, item);
          }
        });
      }

      // 更新组件数据
      this.smartCheckData = processedData;

      console.log('Processed smart check data:', this.smartCheckData);
    },

    loadManualReviewData() {
      // 模拟加载人工审核数据
      // 在实际项目中，这里应该调用API获取数据
      this.manualReviewData = {
        issues: [],
        results: []
      };
    },
    getTabCount(tabKey) {
      return this.smartCheckData[tabKey] ? this.smartCheckData[tabKey].length : 0;
    },
    getTabItems(tabKey) {
      return this.smartCheckData[tabKey] || [];
    },
    handleLocateOriginal(item) {
      // 定位原文：根据catalog值查找并选择对应的树节点
      if (!item || !item.catalog) {
        this.$message.warning('无法定位：缺少目录信息');
        return;
      }

      const catalog = item.catalog;

      // 在树形数据中查找匹配的节点
      const findNodeByCatalog = (nodes, targetCatalog) => {
        for (let node of nodes) {
          // 检查当前节点的ID是否匹配catalog
          if (node.id === targetCatalog) {
            return node;
          }
          // 递归查找子节点
          if (node.children && node.children.length > 0) {
            const found = findNodeByCatalog(node.children, targetCatalog);
            if (found) return found;
          }
        }
        return null;
      };

      const targetNode = findNodeByCatalog(this.treeData, catalog);

      if (targetNode && this.$refs.tree) {
        // 设置当前选中的节点
        this.$refs.tree.setCurrentKey(targetNode.id);

        // 展开到该节点的路径
        const expandPath = [];
        const findPath = (nodes, targetId, path = []) => {
          for (let node of nodes) {
            const currentPath = [...path, node.id];
            if (node.id === targetId) {
              return currentPath;
            }
            if (node.children && node.children.length > 0) {
              const found = findPath(node.children, targetId, currentPath);
              if (found) return found;
            }
          }
          return null;
        };

        const pathToNode = findPath(this.treeData, targetNode.id);
        if (pathToNode) {
          // 展开路径上的所有父节点
          pathToNode.forEach(nodeId => {
            this.$refs.tree.store.nodesMap[nodeId]?.expand();
          });
        }

        // 触发节点点击事件，加载对应内容
        this.handleNodeClick(targetNode);

        this.$message.success(`已定位到：${targetNode.name || '相关内容'}`);
      } else {
        this.$message.warning('未找到对应的文档节点');
        console.warn('Could not find tree node for catalog:', catalog);
      }
    },
    handleAcceptItem(itemId) {
      // 采纳建议
      this.updateItemStatus(itemId, 'accepted');
      this.$message.success('已采纳该建议');
    },
    handleRejectItem(itemId) {
      // 不采纳建议
      this.updateItemStatus(itemId, 'rejected');
      this.$message.success('已拒绝该建议');
    },
    updateItemStatus(itemId, status) {
      // 更新项目状态
      for (const tabKey in this.smartCheckData) {
        const items = this.smartCheckData[tabKey];
        const item = items.find(item => item.id === itemId);
        if (item) {
          item.status = status;
          break;
        }
      }
    },

    // 人工审核问题相关事件处理
    handleEditManualItem(itemId) {
      console.log('编辑人工审核项目:', itemId);
      // 这里可以添加编辑相关的逻辑
    },

    handleSaveManualItem(data) {
      console.log('保存人工审核项目:', data);
      // 在实际项目中，这里应该调用API保存数据
      const item = this.manualReviewData.issues.find(issue => issue.id === data.id);
      if (item) {
        item.content = data.content;
        item.isModified = true;
        // 模拟API调用
        this.$message.success('保存成功');
      }
    },

    handleDeleteManualItem(itemId) {
      console.log('删除人工审核项目:', itemId);
      // 在实际项目中，这里应该调用API删除数据
      const index = this.manualReviewData.issues.findIndex(issue => issue.id === itemId);
      if (index !== -1) {
        this.manualReviewData.issues.splice(index, 1);
        this.$message.success('删除成功');
      }
    },

    // 人工审核表单相关事件处理
    handleSaveManualReview(formData) {
      console.log('保存人工审核结果:', formData);
      // 在实际项目中，这里应该调用API保存数据
      // 示例API调用：
      // this.$callApi('saveManualReviewResult', formData, (result) => {
      //   this.$message.success('审核结果保存成功');
      // });
    },

    handleManualReviewSaveSuccess(formData) {
      console.log('人工审核结果保存成功:', formData);
      // 可以在这里执行保存成功后的逻辑
      // 例如：刷新数据、跳转页面等
    },

    handleResetManualReview() {
      console.log('重置人工审核表单');
      this.manualReviewFormData = {
        reviewComment: '',
        reviewResult: ''
      };
    },
    handleNodeClick(node) {
    console.log("handleNodeClick", node);
      this.tableData = [];
      this.totalAmountPrefixMark = "";
      this.annotate = ""
      this.auditComment = "" // 清空审核意见
      this.isEditBtn = node?.ifEdit
      this.treeId = node?.id;
      this.treeObj = node;
      if(this.moduleType == '编制采购文件') {
        node.needRevise = '否'
      }
      this.cusName = node?.name
      this.contentType = node?.contentType;
      this.contentCategory = node?.type;
      this.isSLing = false;
      if (["富文本", "表格"].includes(this.contentType)) {
        this.emptText = false;
        this.isEdit = false;
      } else {
        this.emptText = true;
      }
      if (this.contentType === null) {
        this.emptText = true;
        this.contentType = null;
      }
      // 如果是采购编制文件、采购文件审核
      if (!this.isCustomBtn && !this.isSupplier) {
        this.getContent();
        this.getAnnotations();
      } else if(this.isSupplier) {
         this.getSupContent();
      } else {
        this.handleContent();
      }
      this.ifEdit = node?.ifEdit === "否"
      if(this.projectInfoStatus === '是' && !this.isAudit) {
        this.ifEdit = false
      }
      if(this.isDetail) {
        this.ifEdit = true
      }
    },
    getContent() {
      // listContentPurFileConfigCatalog查内容  bizid目录id、isAudit是否审核
      this.$callApiParams(
        "listContentPurFileConfigCatalog",
        { bizid: this.treeId, isAudit: this.moduleType === '编制采购文件' ? '否' :this.projectInfoStatus },
        (result) => {
          // 富文本/表格内容
          if (this.contentType === "富文本") {
            this.tinymceContent = result?.data[0]?.content;
            this.$refs.tinymceRef.setInitVal(this.tinymceContent);
            // 关键词显示只有在制作模版的时候 才存在 其他的不赋值 不显示
            if(!this.isAudit) {
              this.totalAmountPrefixMark = result?.data[0]?.totalAmountPrefixMark
                ? result?.data[0]?.totalAmountPrefixMark
                : null;
            }
          } else {
            this.tableData = result?.data || [];
          }
          return true;
        }
      );
    },
    getAnnotations() {
      this.$callApiParams(
        "selectContentAnnotatePurFileConfigCatalog",
        { bizid: this.treeId },
        (result) => {
          this.annotate = result?.data;
          return true;
        }
      );
    },
    getSupContent() {
      let obj = { bizid: this.treeId}
      if(this.cusName === '特殊企业声明函'){
        obj.discountType = this.discountType
      }
      this.$callApiParams(
        "listContentBidFileConfigCatalog",
        { ...obj },
        (result) => {
          // 富文本/表格内容
          if (this.contentType === "富文本") {
            this.tinymceContent = result?.data[0]?.content;
            if(result?.data[0]?.discountType) {
              this.discountType = result?.data[0]?.discountType;
            }
            this.$refs.tinymceRef.setInitVal(this.tinymceContent);
            // 关键词显示只有在制作模版的时候 才存在 其他的不赋值 不显示
            if(!this.isAudit) {
              this.totalAmountPrefixMark = result?.data[0]?.totalAmountPrefixMark
                ? result?.data[0]?.totalAmountPrefixMark
                : null;
            }
          } else {
            this.tableData = result?.data || [];
          }
          return true;
        }
      );
    },
    // 获取富文本/表格内容  制作模版逻辑
    handleContent() {
      // this.isSaved = false;
      this.$callApiParams(
        "listContentPurFileTemplateCatalog",
        { bizid: this.treeId },
        (result) => {
          // 富文本/表格内容
          if (this.contentType === "富文本") {
            this.tinymceContent = result?.data[0]?.content;
            this.$refs.tinymceRef.setInitVal(this.tinymceContent);
            this.totalAmountPrefixMark = result?.data[0]?.totalAmountPrefixMark
              ? result?.data[0]?.totalAmountPrefixMark
              : null;
          } else {
            this.tableData = result?.data || [];
          }
          return true;
        }
      );
    },
    // 获取所有表格元素添加样式
    addTableBordersIfMissing(htmlString) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, 'text/html');
      const tables = doc.getElementsByTagName('table');

      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];
        if (!table.getAttribute('border')) {
          table.setAttribute('border', '1');
        }
        table.setAttribute('style', 'width: 100%; border-collapse: collapse;');
        const tds = table.getElementsByTagName('td');
        for (let j = 0; j < tds.length; j++) {
          const td = tds[j];
          const spans = td.getElementsByTagName('span');
          if (spans.length > 0) {
            for (let k = 0; k < spans.length; k++) {
              td.textContent = td.textContent.replace(spans[k], spans[k].textContent); // 移除span并替换为文本内容
              td.textContent = td.textContent.replace(/\u00A0/g, ''); // 移除td中的&nbsp;
            }
          } else {
            const textNodes = td.childNodes;
            for (let n = 0; n < textNodes.length; n++) {
              if (textNodes[n].nodeType === Node.TEXT_NODE) {
                textNodes[n].nodeValue = textNodes[n].nodeValue.replace(/\u00A0/g, ''); // 移除td中的&nbsp;
              }
            }
          }
        }
      }
      return doc.documentElement.innerHTML;
    },
    // 保存
    handleSure(type) {
      this.isSLing = true;
      let obj = {};
      let fetchApi;
      if (this.isAudit && !this.isCustomBtn) {
        const annotate = this.annotate;
        obj = {
          purFileConfigCatalogBizid: this.treeId,
          annotate,
          // 批注
        };
        fetchApi = "auditPurFileConfigCatalogContent";
      } else if (!this.isCustomBtn && !this.isAudit&& !this.isSupplier) {
        obj = {
          purFileConfigCatalogContentList: [{ content: this.addTableBordersIfMissing(this.tinymceContent) }],
          purFileConfigCatalogBizid: this.treeId,
        };
        fetchApi = "savePurFileConfigCatalogContent";
      } else if (this.isSupplier) {
        obj = {
          bidFileConfigCatalogContentList: [{ content: this.addTableBordersIfMissing(this.tinymceContent)}],
          bidFileConfigCatalogBizid: this.treeId,
          discountType: this.discountType,
        };
        fetchApi = "saveBidFileConfigCatalogContent";
      } else if(type === 'annotate') {
        obj = {
         purFileTemplateCatalogContentList: [{ content: this.addTableBordersIfMissing(this.tinymceContent) }],
         purFileConfigCatalogBizid: this.treeId,
        }
        fetchApi = "auditPurFileConfigCatalogContent";
      } else {
        obj = {
          purFileTemplateCatalogContentList: [{ content: this.addTableBordersIfMissing(this.tinymceContent) }],
          purFileTemplateCatalogBizid: this.treeId,
        };
        fetchApi = "savePurFileTemplateCatalogContent";
      }

      if (this.totalAmountPrefixMark) {
        obj.totalAmountPrefixMark = this.totalAmountPrefixMark;
      }
      this.$callApi(
        fetchApi,
        obj,
        (result) => {
          this.isEdit = true;
          // this.isSaved = true;
          this.isSLing = false;
          this.handleNodeClick(this.treeObj)
        },
        () => {
          this.isSLing = false;
        },
        { isSave: false }
      );
    },

    changeContent() {
      if (this.contentType === "表格") return;
      this.showContent = true;
      this.$nextTick(() => {
        this.$refs.tinymceRef.setInitVal(this.tinymceContent);
      });
    },
    // 项目类别查询
    projectTypeChange(value) {
        this.discountType = value
        if(value === '以上都不是') return
        this.handleNodeClick(this.treeObj)
    },

    // 保存审核意见
    handleSaveAuditComment() {
      if (!this.auditComment.trim()) {
        this.$message.warning('请输入审核意见');
        return;
      }

      this.auditSaving = true;

      // 构建请求参数
      const requestBody = {
        "审核页面人工": {
          "projectInfoBizid": this.projectInfoBizid || "1947114097698947073",
          "type": "人工审核",
          "catalog": this.treeObj?.name || "原文目录",
          "content": this.auditComment.trim()
        }
      };

      // 保存当前审核意见内容，用于后续显示
      const savedComment = this.auditComment.trim();

      // 调用 saveCheckResult API
      this.$callApiParams('saveCheckResult', {
        page: 'audit', // 查询参数
        ...requestBody
      }, (result) => {
        if (result.success) {
          this.$message.success('审核意见保存成功');

          // 清空输入框
          this.auditComment = '';

          // 刷新人工审核发现的问题列表
          this.refreshManualReviewData(savedComment);

          // 如果智能检查面板已打开，切换到人工审核发现的问题标签页
          if (this.showSmartCheck) {
            this.activeMainTab = 'manual-issues';
          }
        } else {
          this.$message.error(result.message || '保存失败，请重试');
        }
        this.auditSaving = false;
        return true;
      }, (error) => {
        this.$message.error('保存失败，请重试');
        this.auditSaving = false;
      });
    },

    // 刷新人工审核数据
    refreshManualReviewData(savedComment) {
      // 这里可以调用API重新获取人工审核数据
      // 暂时模拟添加新的审核意见到列表中
      const newIssue = {
        id: 'manual_' + Date.now(),
        reviewDate: new Date().toLocaleString('zh-CN'),
        content: savedComment,
        status: 'unprocessed',
        treeNodeId: this.treeId,
        isModified: false
      };

      this.manualReviewData.issues.unshift(newIssue);
    }
  },
  mounted() {
  }
};
</script>

<style scoped lang="scss">
.containerWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px; /* 整个布局内边距 */
}

.top-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.search-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.button-section {
  display: flex;
  .tags {
    margin-left: 12px;
    height: 30px;
    width: 100%;
    display: flex;
  }
  .custom-button {
    color: #409eff;
    border: 1px solid #409eff !important;
    background-color: #e8f4ff;
    font-weight: bold;
    font-size: 14px;
    padding: 0px 12px;
    line-height: 30px;
    height: 30px;
  }
}

.main-section {
  display: flex;
  flex: 1;
}

.tree-section {
  width: 360px;
  border: 1px solid #ddd;
  position: relative;
  transition: width 0.5s ease;
  background-color: #fff;
  height: calc(70vh - 30px); /* 固定高度，与其他区域保持一致 */

  &.collapsed {
    width: 30px !important;
  }

  .retract-left {
    position: absolute;
    right: -10px;
    top: 40%;
    z-index: 10;
  }

  .tree-content {
    height: 100%; /* 占满整个容器高度 */
    overflow: hidden; /* 防止内容溢出 */
    padding: 10px; /* 内边距移到这里 */
    box-sizing: border-box;
  }

  .tree-inner {
    height: 100%;
    overflow-y: auto; /* 只有内容区域有滚动条 */
  }

  &.collapsed .tree-content {
    padding: 0; /* 收缩状态下移除内边距 */
  }
}

.table-section {
  flex: 1;
  border: 1px solid #ddd;
  margin-left: 20px;
  display: flex;
  height: 100%;
  overflow-x: hidden;
  position: relative;
  .preview-text {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 0;
    width: 100%;
  }
}

.smart-check-container {
  width: 400px;
  margin-left: 20px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: width 0.5s ease;
  border: 1px solid #ddd;

  &.collapsed {
    width: 30px !important;
  }

  .retract-right {
    position: absolute;
    left: -10px;
    top: 40%;
    z-index: 10;
    margin-left: 0;
    margin-right: -5px;
  }
}
.table-section,
.list-section {
  overflow-y: auto; /* 添加垂直滚动条 */
  height: calc(70vh - 30px);
}
.tree-inner {
  .custom-node {
    position: relative;
    display: flex;
    align-items: center;
    .red-dot {
      width: 6px;
      height: 6px;
      background-color: red;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
}
[class^="el-icon-"] {
  font-family: element-icons !important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
}
::v-deep .el-tree-node__expand-icon {
  font-size: 20px !important;
}
.iconBtn {
  i {
    cursor: pointer;
    margin: 0 3px;
  }

  .addicon {
    font-size: 26px;
    color: #67c23a;
  }

  .delicon {
    font-size: 26px;
    color: #f56c6c;
  }

  .diricon {
    font-size: 22px;
    color: #409eff;
  }

  .editicon {
    font-size: 26px;
    color: #409eff;
  }
}

// 缩进按钮样式
.retract-block {
  padding: 4px 0;
  box-sizing: border-box;
  width: 10px;
  height: 69px;
  line-height: 59px;
  color: white;
  background: rgba(31, 127, 255, 0.5);
}

// 智能辅助检查面板样式
.smart-check-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 主标签页样式
.main-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;

  ::v-deep .el-tabs__header {
    margin: 0;
    padding: 0 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
  }

  ::v-deep .el-tabs__content {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }

  ::v-deep .el-tab-pane {
    height: 100%;
    overflow-y: auto;
  }

  ::v-deep .el-tabs__nav-scroll {
    height: 44px;
    line-height: 44px;
  }
}

// 智能检查内容区域
.smart-check-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.smart-check-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;

  ::v-deep &.el-tabs.el-tabs--top {
    height: 100%;
  }
  ::v-deep .el-tabs__header {
    margin: 0;
    padding: 5px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed !important;
  }

  ::v-deep .el-tabs__content {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }

  ::v-deep .el-tab-pane {
    height: 100%;
    overflow-y: auto;
  }

  ::v-deep .el-tabs__nav-scroll {
    height: 32px;
    line-height: 32px;
  }
}

.tab-badge {
  ::v-deep .el-badge__content {
    background-color: #f56c6c;
    border: none;
    font-size: 10px;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    min-width: 16px;
  }
}

.tab-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;

  // 人工审核发现的问题标签页特殊样式
  &.manual-issues-content {
    padding: 0;
    overflow: hidden;
  }

  // 人工审核结果标签页特殊样式
  &.manual-results-content {
    padding: 0;
    overflow: hidden;
  }
}

.empty-state {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 20px;
}

// 审核意见样式
.audit-review-section {
  width: 100%;
  padding: 0 16px 5px 16px;
  //border-top: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
  margin-top: 10px;

  .audit-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 12px;
    padding-top: 10px;
  }

  .audit-form {
    display: flex;
    align-items: flex-start;
    gap: 12px;

    .audit-form-left {
      flex: 1;

      .audit-textarea {
        ::v-deep .el-textarea__inner {
          resize: vertical;
          min-height: 80px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }

    .audit-form-right {
      display: flex;
      align-items: center;

      .audit-save-btn {
        height: 32px;
        padding: 0 20px;
        font-size: 14px;
        border-radius: 4px;

        &:disabled {
          background-color: #f5f7fa;
          border-color: #e4e7ed;
          color: #c0c4cc;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
