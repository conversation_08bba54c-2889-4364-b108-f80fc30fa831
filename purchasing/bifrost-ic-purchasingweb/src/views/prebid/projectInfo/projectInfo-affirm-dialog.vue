<template>
    <div>
        <el-dialog ref="auditDialog" fullscreen :title='titleText' append-to-body :width="dialogWidth" class="auditDialogStyle dialog-style1" :close-on-click-modal="false" :visible.sync="auditVisible">
            <div class="auditDialogBox" style="height:750px;">
                <div class="auditDialogLeft" style="padding:10px 10px 10px 20px;">
                    <produce-pur-fileInfo ref="purFileInfo" />
                </div>
                <div class="auditDialogRight">
                    <div class="auditLogBox" style="height:360px;">
                        <el-timeline>
                            <el-timeline-item
                            v-for="(activity, index) in activities"
                            :key="index"
                            :icon="activity.icon"
                            :type="activity.type"
                            :color="activity.color"
                            :size="activity.size"
                            :timestamp="activity.timestamp">
                            <div :style="getColor(activity.nodeName)">
                              {{activity.nodeName}} <span style="color: #606266">【{{activity.auditUser}}】
                              {{activity.createTime}}</span>

                            </div>
                          </el-timeline-item>
                        </el-timeline>
                    </div>
                    <div class="dialogBox" style="padding:40px 20px 20px; margin-top:20px">
                        <el-form ref="auditForm" :rules="rules" :model="auditForm" label-width="80px" >
                            <el-form-item label="审批意见" prop="option">
                                <el-input type="textarea" v-model="auditForm.option" :rows="6" maxlength="200" placeholder="请输入审批意见"></el-input>
                            </el-form-item>
                            <el-form-item label="审核结果" prop="confirmResut">
                                <el-radio-group v-model="auditForm.confirmResut" @change="handleChange">
                                    <el-radio label="0">通过</el-radio>
                                    <el-radio label="1">不通过</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-form>
                        <div style="margin-top:30px;text-align:center">
                            <el-button class="btn-normal" type="primary" icon="el-icon-edit" @click="saveAudit">确定</el-button>
                            <!-- <el-button class="btn-normal" @click="close">返回</el-button> -->
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import ProducePurFileInfo from './produce-pur-fileInfo'
export default {
    name: 'base-pur-audit-dialog',
    components: {ProducePurFileInfo},
    props:{
        dialogWidth:{
            type: String,
            default:'1350px'
        }
    },
    data() {
        return {
            titleText:"审核",
            auditVisible:false,
            projectInfoStatus:"",
            ids:"",
            dataType:"",
            auditForm:{
                option:"",
                confirmResut:"0"
            },
            rules:{
                option:[{ required: true, message: '请输入审批意见', trigger: 'blur' }],
                confirmResut:[{ required: true, message: '请选择审核结果', trigger: 'change' }]
            },

            activities: [],
        };
    },

    mounted() {

    },

    methods: {
        show(row,dataType,text,showEditBidFile){
            this.clearData()
            this.auditVisible = true
            this.dataType = dataType
            this.projectInfoStatus = row.getRowData().projectInfoStatus
            this.pageState = row.getRowData().isUpdateProcurementDocument
            this.ids = row.getRowData().bizid
            this.$nextTick(()=>{
                 this.$refs.purFileInfo.init(row, text, false, showEditBidFile,false)
            })
            this.initAuditLog(this.ids)
            this.$nextTick(()=>{
                if(this.pageState == '是'){

                    this.auditForm.confirmResut = "1"
                }
            })
        },
        initAuditLog(id){
            this.$callApiParams('getWfRemarkByDataId',
          { dataId: id, dataType: this.dataType  }, (result) => {
            this.activities = result.data
            return true
          })
        },

        clearData(){
            this.auditForm = {
                option:"",
                confirmResut:"0"
            }
        },

        handleChange(){
            if(this.pageState == '是'){
                this.auditForm.confirmResut = "1"
                this.$message.error('请确认采购文件内容，填写审批意见并退回采购文件制作人修改');
            }
        },

        getColor(content) {
            if (content.indexOf('退回') !== -1) {
                return { 'color': 'red' }
            }
            if (content.indexOf('撤销') !== -1) {
                return { 'color': '#c4cad5' }
            }
        },

        saveAudit(){
            this.$refs['auditForm'].validate((valid) => {
                if (valid) {
                    let params = Object.assign({
                        id:this.ids,
                        projectInfoStatus:this.projectInfoStatus
                    }, this.auditForm)
                    this.$callApiParams('confirmProjectInfo', params, (result) => {
                    if(result.success){
                        if(this.auditForm.confirmResut=="0"){
                            this.sendAudit()
                        }
                        this.$parent.init()
                        this.auditVisible = false
                    }
                })
                }
            })
        },

        //送审
        sendAudit(){
            let params = {
                ids:this.ids,
                dataType:'ProjectInfoEntity',
                apiKey: 'WFSUBMIT'
            }
            this.$callApiParams('WFSUBMIT'
                , params, result => {
                return true
            })
        }


    },
};
</script>

<style lang="scss" scoped>

</style>
