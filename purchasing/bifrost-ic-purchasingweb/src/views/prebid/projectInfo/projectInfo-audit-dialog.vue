<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-23 16:22:19
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-15 09:26:42
-->
<template>
  <base-pur-audit-dialog ref="auditDialog" @auditSucceed="auditSucceed">
    <template #infoLeft>
        <produce-pur-fileInfo ref="purFileInfo" />
    </template>
  </base-pur-audit-dialog>
</template>

<script>
import BasePurAuditDialog from '@/components/page/base-pur-audit-dialog'
import ProducePurFileInfo from './produce-pur-fileInfo'
export default {
  name: 'projectInfo-audit-dialog',
  components: {BasePurAuditDialog,ProducePurFileInfo},
  provide() {
      return {
          deleteData: true
      }
  },
  data() {
      return {
          attData: {
              main: {
              formType: ''
              },
              attList: [],
              extData: {
              currentUser: '',
              attTempId: '',
              initRefDataVoFromId: undefined,
              batchDownloadName: '',
              attTypeTableName: '',
              bizDataId: ''
              },
              data: {
              id: ''
              }
          }
      };
  },
  mounted() {

  },
  methods: {
    show(row,dataType,text, showEditBidFile){
      this.$refs.auditDialog.show(row,dataType)
      this.$nextTick(()=>{
        //text=="审核"， 这里文件的按钮不显示删除，显示上传，只针对采购文件审核页
          this.$refs.purFileInfo.init(row,text, false, showEditBidFile, false)
      })
    },

    //审核成功处理
    auditSucceed(){
      this.$parent.reload()
    }
  }
}
</script>
